import os
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'  # Suprime a maioria das mensagens do TensorFlow

from dataclasses import dataclass, asdict, field
import pandas as pd
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import NoSuchElementException, StaleElementReferenceException
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.actions.wheel_input import ScrollOrigin
import time

@dataclass
class Business:
    """Armazena os dados de um negócio."""
    name: str = None
    address: str = None
    website: str = None
    phone_number: str = None

@dataclass
class BusinessList:
    """Armazena uma lista de objetos Business e permite salvar os resultados em Excel ou CSV."""
    business_list: list[Business] = field(default_factory=list)

    def dataframe(self):
        """Transforma a lista de negócios em um DataFrame do pandas."""
        return pd.json_normalize((asdict(business) for business in self.business_list), sep="_")

    def save_to_excel(self, filename, save_dir):
        """Salva o DataFrame em um arquivo Excel."""
        if save_dir:
            full_path = f'{save_dir}/{filename}.xlsx'
            self.dataframe().to_excel(full_path, index=False)
            return f"Arquivo salvo em: {full_path}"
        return "Diretório não especificado"

    def save_to_csv(self, filename, save_dir):
        """Salva o DataFrame em um arquivo CSV."""
        if save_dir:
            full_path = f'{save_dir}/{filename}.csv'
            self.dataframe().to_csv(full_path, index=False, sep=';')
            return f"Arquivo salvo em: {full_path}"
        return "Diretório não especificado"

def move_map(navegador, direction='right', distance=10):
    """
    Move o mapa na direção especificada ou executa zoom.
    direction: 'right', 'left', 'up', 'down', 'zoom_in', 'zoom_out'
    distance: número de pressionamentos da tecla (controla a distância) ou nível de zoom
    """
    try:
        # Primeiro clica no mapa para garantir que está focado
        map_element = navegador.find_element(By.CLASS_NAME, 'widget-scene')
        action = ActionChains(navegador)
        action.move_to_element(map_element).click().perform()
        time.sleep(1)

        if direction == 'zoom_out':
            # Zoom out usando Ctrl + scroll ou tecla -
            for _ in range(distance):
                action.key_down(Keys.CONTROL).send_keys('-').key_up(Keys.CONTROL).perform()
                time.sleep(0.5)
        elif direction == 'zoom_in':
            # Zoom in usando Ctrl + scroll ou tecla +
            for _ in range(distance):
                action.key_down(Keys.CONTROL).send_keys('+').key_up(Keys.CONTROL).perform()
                time.sleep(0.5)
        else:
            # Mapeia a direção para a tecla correspondente
            key_map = {
                'right': Keys.ARROW_RIGHT,
                'left': Keys.ARROW_LEFT,
                'up': Keys.ARROW_UP,
                'down': Keys.ARROW_DOWN
            }

            # Pressiona a tecla várias vezes para mover o mapa
            key = key_map.get(direction, Keys.ARROW_RIGHT)
            for _ in range(distance):
                action.send_keys(key).perform()
                time.sleep(0.1)

        # Aguarda um pouco para o mapa carregar
        time.sleep(4)  # Aumentado para dar mais tempo para carregar

    except Exception as e:
        print(f"[ERRO] Falha ao mover mapa: {str(e)}")
        time.sleep(2)

def reset_search_in_new_area(navegador, search_term, callback=None):
    """
    Reseta a busca em uma nova área do mapa.
    Útil quando o padrão de navegação se esgota.
    """
    try:
        if callback:
            callback("[INFO] Resetando busca em nova área...")

        # Clica na caixa de busca e limpa
        search_box = navegador.find_element(By.XPATH, '//*[@id="searchboxinput"]')
        search_box.clear()
        time.sleep(1)

        # Insere o termo de busca novamente
        search_box.send_keys(search_term)
        time.sleep(2)

        # Clica no botão de busca
        navegador.find_element(By.XPATH, '//*[@id="searchbox-searchbutton"]').click()
        time.sleep(8)

        if callback:
            callback("[INFO] Busca resetada com sucesso!")

        return True

    except Exception as e:
        if callback:
            callback(f"[ERRO] Falha ao resetar busca: {str(e)}")
        return False

def get_spiral_navigation_pattern(max_radius=15):
    """
    Gera um padrão de navegação em espiral expandido para cobrir muito mais área do mapa.
    Retorna uma lista de tuplas (direção, distância) para navegar em espiral.
    """
    pattern = []

    # Padrão em espiral: direita, baixo, esquerda, cima, aumentando a distância
    directions = ['right', 'down', 'left', 'up']

    for radius in range(1, max_radius + 1):
        for i, direction in enumerate(directions):
            # Aumenta progressivamente a distância para cobrir mais área
            base_distance = radius * 12  # Aumentado de 8 para 12

            # Adiciona movimento extra nas direções horizontais para cobrir mais área
            if direction in ['right', 'left']:
                distance = base_distance + (radius * 5)  # Movimento extra horizontal
            else:
                distance = base_distance

            pattern.append((direction, distance))

            # Adiciona movimentos extras para cobrir melhor a área
            if direction in ['right', 'down'] and radius > 1:
                pattern.append((direction, distance // 2))

            # Adiciona movimentos diagonais simulados para cobrir cantos
            if radius > 3:
                if direction == 'right':
                    pattern.append(('down', distance // 3))
                elif direction == 'down':
                    pattern.append(('left', distance // 3))
                elif direction == 'left':
                    pattern.append(('up', distance // 3))
                elif direction == 'up':
                    pattern.append(('right', distance // 3))

    return pattern

def get_extended_navigation_pattern():
    """
    Gera um padrão de navegação estendido que inclui zoom out/in e navegação por quadrantes.
    Usado quando o padrão espiral se esgota.
    """
    pattern = []

    # Primeiro, adiciona movimentos de zoom out para ver área maior
    pattern.append(('zoom_out', 3))

    # Navega pelos quadrantes principais
    quadrants = [
        ('right', 50), ('down', 50),  # Quadrante SE
        ('left', 100), ('down', 50),  # Quadrante SW
        ('left', 50), ('up', 100),    # Quadrante NW
        ('right', 100), ('up', 50),   # Quadrante NE
        ('right', 50), ('down', 50)   # Volta ao centro
    ]

    pattern.extend(quadrants)

    # Zoom in para ver detalhes
    pattern.append(('zoom_in', 2))

    return pattern

def detect_no_more_results(navegador, previous_count, stuck_threshold=5):
    """
    Detecta se não há mais resultados disponíveis na área atual.
    Retorna True se não há mais resultados, False caso contrário.
    """
    try:
        # Conta os elementos atuais
        list_elem = navegador.find_elements(By.CLASS_NAME, 'hfpxzc')
        current_count = len(list_elem)

        # Se o número de elementos não mudou por várias tentativas, provavelmente não há mais
        return current_count == previous_count

    except Exception:
        return True

def get_current_map_info(navegador):
    """
    Obtém informações sobre a área atual do mapa para debug.
    """
    try:
        # Tenta obter informações da URL ou elementos visíveis
        current_url = navegador.current_url

        # Procura por elementos que indiquem a localização atual
        location_elements = navegador.find_elements(By.CSS_SELECTOR, '[data-value]')

        info = {
            'url': current_url,
            'elements_count': len(navegador.find_elements(By.CLASS_NAME, 'hfpxzc')),
            'timestamp': time.time()
        }

        return info
    except Exception:
        return {'error': 'Não foi possível obter informações do mapa'}

def main_query(search_for, total, location, save_dir, file_format, callback=None):
    """
    Executa a consulta no Google Maps e extrai os dados dos estabelecimentos.

    Parâmetros:
      search_for: termo de busca (ex.: "restaurante Rio de Janeiro")
      total: número de resultados desejados
      location: localização para refinar a consulta
      save_dir: diretório onde os resultados serão salvos
      file_format: "excel" ou "csv"
      callback: função opcional para receber atualizações de status
    """
    # Configura o Chrome para iniciar maximizado
    from selenium.webdriver.chrome.options import Options
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    chrome_options.add_argument("--disable-gpu")  # Desativa aceleração por hardware
    chrome_options.add_argument("--enable-unsafe-swiftshader")  # Permite SwiftShader
    chrome_options.add_argument("--disable-software-rasterizer")  # Evita problemas com renderização
    chrome_options.add_argument("--disable-dev-shm-usage")  # Evita problemas de memória compartilhada
    chrome_options.add_argument("--no-sandbox")  # Aumenta compatibilidade
    chrome_options.add_experimental_option("detach", True)  # Mantém o navegador aberto
    chrome_options.add_experimental_option("excludeSwitches", ['enable-automation'])  # Remove barra de automação
    chrome_options.add_experimental_option("excludeSwitches", ['enable-logging'])  # Remove logs do DevTools

    if callback:
        callback("[INFO] Iniciando o navegador Chrome em modo headless...")

    navegador = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=chrome_options)

    if callback:
        callback("[INFO] Abrindo Google Maps...")
    navegador.get("https://www.google.com.br/maps")
    time.sleep(3)

    # Insere a localização e executa a busca inicial
    if callback:
        callback(f"[INFO] Buscando localização: {location}...")
    navegador.find_element(By.XPATH, '//*[@id="searchboxinput"]').send_keys(location)
    time.sleep(2)
    navegador.find_element(By.XPATH, '//*[@id="searchbox-searchbutton"]').click()
    time.sleep(15)
    navegador.find_element(By.XPATH, '//*[@id="searchbox"]/div[2]/button').click()
    time.sleep(5)

    # Realiza a busca do termo combinado (negócio + localização)
    if callback:
        callback(f"[INFO] Buscando termo: {search_for}...")
    navegador.find_element(By.XPATH, '//*[@id="searchboxinput"]').send_keys(search_for)
    navegador.find_element(By.XPATH, '//*[@id="searchbox-searchbutton"]').click()
    time.sleep(10)

    business_list = BusinessList()
    i = 0

    # Nova estratégia de navegação em espiral expandida
    spiral_pattern = get_spiral_navigation_pattern(max_radius=15)  # Aumentado significativamente
    extended_pattern = get_extended_navigation_pattern()  # Padrão adicional para áreas maiores
    pattern_index = 0
    extended_pattern_index = 0

    # Limites muito mais generosos para navegação contínua
    max_navigation_attempts = len(spiral_pattern) + len(extended_pattern) + 50  # Muito mais tentativas
    navigation_attempts = 0
    areas_without_results = 0  # Contador de áreas sem novos resultados
    max_empty_areas = 8  # Aumentado de 3 para 8 áreas vazias antes de tentar reset
    reset_attempts = 0  # Contador de tentativas de reset
    max_reset_attempts = 5  # Máximo de resets antes de parar definitivamente

    while i < total and (navigation_attempts < max_navigation_attempts or reset_attempts < max_reset_attempts):
        previously_counted = 0
        stuck_count = 0

        while True:
            list_elem = navegador.find_elements(By.CLASS_NAME, 'hfpxzc')
            if not list_elem:
                if callback:
                    callback("[ERRO] Nenhum elemento encontrado na página. Verifique se a consulta está correta ou se há resultados para esta região.")
                return "[ERRO] Nenhum elemento encontrado na página. Verifique se a consulta está correta ou se há resultados para esta região."

            action = ActionChains(navegador)
            try:
                action.move_to_element(list_elem[-1]).perform()
            except Exception:
                list_elem = navegador.find_elements(By.CLASS_NAME, 'hfpxzc')
                action.move_to_element(list_elem[-1]).perform()
            time.sleep(5)

            scroll_origin = ScrollOrigin.from_element(list_elem[-1])
            action.scroll_from_origin(scroll_origin, 0, 1200).perform()
            time.sleep(20)
            action.scroll_from_origin(scroll_origin, 0, 250).perform()

            current_count = len(list_elem)
            if current_count == previously_counted:
                stuck_count += 1
                if stuck_count >= 3:  # Se ficar preso 3 vezes, consideramos que chegamos ao limite
                    break
            else:
                stuck_count = 0
                previously_counted = current_count

        # Processa os elementos encontrados
        list_elem = navegador.find_elements(By.CLASS_NAME, 'hfpxzc')
        for element in list_elem[i:]:
            if i >= total:
                break

            try:
                time.sleep(2)
                navegador.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
                time.sleep(2)
                try:
                    element.click()
                except Exception as click_err:
                    error_msg = f"[ERRO] Falha ao clicar no elemento {i}: {str(click_err)}"
                    if callback:
                        callback(error_msg)
                    print(error_msg)
                    i += 1
                    continue
                time.sleep(6)

                # XPaths para extração dos dados
                name_xpath = '//*[@id="QA0Szd"]/div/div/div[1]/div[3]/div/div[1]/div/div/div[2]/div[2]/div/div[1]/div[1]/h1'
                address_xpath = '//button[@data-item-id="address"]//div[contains(@class, "fontBodyMedium")]'
                website_xpath = '//a[@data-item-id="authority"]//div[contains(@class, "fontBodyMedium")]'
                phone_number_xpath = '//button[contains(@data-item-id, "phone:tel:")]//div[contains(@class, "fontBodyMedium")]'

                business = Business()
                try:
                    business.name = navegador.find_element(By.XPATH, name_xpath).text or ''
                    if not business.name:
                        business.name = navegador.find_element(By.XPATH, "//h1[contains(@class, 'fontHeadlineLarge')]").text or ''
                except NoSuchElementException:
                    business.name = ''
                try:
                    business.address = navegador.find_element(By.XPATH, address_xpath).text or ''
                except NoSuchElementException:
                    business.address = ''
                try:
                    business.website = navegador.find_element(By.XPATH, website_xpath).text or ''
                except NoSuchElementException:
                    business.website = ''
                try:
                    business.phone_number = navegador.find_element(By.XPATH, phone_number_xpath).text or ''
                except NoSuchElementException:
                    business.phone_number = ''

                business_list.business_list.append(business)
                time.sleep(3)
                i += 1

                if callback:
                    porcentagem = (i / total) * 100
                    callback(f"[PROGRESSO] Processando registro {i} de {total} ({porcentagem:.1f}%)")

            except StaleElementReferenceException:
                error_msg = f"[AVISO] Elemento {i} está desatualizado, tentando próximo registro..."
                if callback:
                    callback(error_msg)
                print(error_msg)
                i += 1
                continue

        # Se ainda não atingimos o total desejado, navega automaticamente
        if i < total:
            initial_count = i

            if callback:
                callback(f"[INFO] Encontrados {i} de {total} registros. Navegando automaticamente para encontrar mais resultados...")

            # Estratégia de navegação em múltiplas fases
            navigation_success = False

            # Fase 1: Padrão espiral expandido
            if pattern_index < len(spiral_pattern):
                direction, distance = spiral_pattern[pattern_index]
                if callback:
                    callback(f"[NAVEGAÇÃO ESPIRAL] Movendo mapa para {direction} (distância: {distance}) - Tentativa {pattern_index + 1}/{len(spiral_pattern)}")

                move_map(navegador, direction, distance)
                pattern_index += 1
                navigation_success = True

            # Fase 2: Padrão estendido com zoom
            elif extended_pattern_index < len(extended_pattern):
                direction, distance = extended_pattern[extended_pattern_index]
                if callback:
                    callback(f"[NAVEGAÇÃO ESTENDIDA] Movendo mapa para {direction} (distância: {distance}) - Tentativa {extended_pattern_index + 1}/{len(extended_pattern)}")

                move_map(navegador, direction, distance)
                extended_pattern_index += 1
                navigation_success = True

            # Fase 3: Navegação aleatória inteligente
            elif areas_without_results < max_empty_areas:
                import random
                directions = ['right', 'down', 'left', 'up', 'zoom_out', 'zoom_in']
                direction = random.choice(directions)

                if direction in ['zoom_out', 'zoom_in']:
                    distance = random.randint(1, 3)
                else:
                    distance = random.randint(20, 40)  # Distâncias maiores para cobrir mais área

                if callback:
                    callback(f"[NAVEGAÇÃO ALEATÓRIA] Movendo mapa para {direction} (distância: {distance})")

                move_map(navegador, direction, distance)
                navigation_success = True

            # Fase 4: Reset da busca em nova área
            elif reset_attempts < max_reset_attempts:
                if callback:
                    callback(f"[RESET] Tentando resetar busca em nova área (tentativa {reset_attempts + 1}/{max_reset_attempts})")

                # Move para uma área bem distante antes do reset
                for _ in range(3):
                    import random
                    direction = random.choice(['right', 'down', 'left', 'up'])
                    move_map(navegador, direction, 50)

                # Reseta a busca
                if reset_search_in_new_area(navegador, search_for, callback):
                    # Reset dos contadores para começar nova busca
                    pattern_index = 0
                    extended_pattern_index = 0
                    areas_without_results = 0
                    navigation_success = True

                reset_attempts += 1

            if navigation_success:
                navigation_attempts += 1

                # Aguarda um pouco mais para o mapa carregar completamente
                time.sleep(10)  # Aumentado para dar mais tempo

                # Verifica se encontrou novos resultados após a navegação
                if i == initial_count:
                    areas_without_results += 1
                    if callback:
                        callback(f"[AVISO] Área sem novos resultados ({areas_without_results}/{max_empty_areas})")
                else:
                    areas_without_results = 0  # Reset contador se encontrou resultados
                    if callback:
                        callback(f"[SUCESSO] Encontrados {i - initial_count} novos resultados nesta área!")
            else:
                # Se não conseguiu navegar, para o loop
                if callback:
                    callback("[INFO] Todas as estratégias de navegação foram esgotadas.")
                break
        else:
            break

    # Finaliza a extração e salva automaticamente os dados
    if callback:
        callback(f"[FINALIZAÇÃO] Extração concluída! Total de empresas encontradas: {i}")
        callback(f"[ESTATÍSTICAS] Tentativas de navegação: {navigation_attempts}")
        callback(f"[ESTATÍSTICAS] Tentativas de reset: {reset_attempts}")
        callback(f"[ESTATÍSTICAS] Áreas sem resultados: {areas_without_results}")

    updated_string = search_for.replace(" ", "_")
    result = None
    if file_format.lower() == "excel":
        result = business_list.save_to_excel(f'maps_data_{updated_string}', save_dir)
    elif file_format.lower() == "csv":
        result = business_list.save_to_csv(f'maps_data_{updated_string}', save_dir)
    else:
        result = "Formato de arquivo inválido."

    if callback:
        callback(result)
        if i < total:
            callback(f"[AVISO] Meta não atingida. Solicitado: {total}, Encontrado: {i}")
            callback("[DICA] Tente aumentar a área de busca ou usar termos mais genéricos.")
        else:
            callback(f"[SUCESSO] Meta atingida! {i} empresas extraídas com sucesso.")

    return result

if __name__ == "__main__":
    search_term = input("Nome para consulta:")
    location = input("Localização para consulta:")
    search_for = f"{search_term} {location}"
    total_str = input("Número de locais para consulta (inteiro):")
    try:
        total = int(total_str)
    except:
        print("Valor inválido para número de locais.")
        exit(1)

    def console_callback(msg, require_response=False):
        print(msg)
        if require_response:
            return input("Sua resposta: ")

    main_query(search_for, total, location, ".", "excel", callback=console_callback)
