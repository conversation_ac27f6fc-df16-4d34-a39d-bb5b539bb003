#!/usr/bin/env python3
"""
Script de teste para demonstrar as melhorias na navegação contínua do Google Maps Scraper.
Este script permite testar diferentes configurações e monitorar o progresso.
"""

import sys
import os
from datetime import datetime

# Adiciona o diretório atual ao path para importar o módulo
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from google_maps_scraper import main_query

def test_callback(message, require_response=False):
    """
    Callback personalizado para monitorar o progresso do scraper.
    """
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")
    
    if require_response:
        return input("Sua resposta: ")

def test_navegacao_continua():
    """
    Testa a navegação contínua com diferentes configurações.
    """
    print("=" * 80)
    print("🚀 TESTE DE NAVEGAÇÃO CONTÍNUA - GOOGLE MAPS SCRAPER")
    print("=" * 80)
    print()
    
    # Configurações de teste
    test_configs = [
        {
            "name": "Teste Pequeno (50 empresas)",
            "search_term": "restaurante",
            "location": "São Paulo, SP",
            "total": 50,
            "description": "Teste rápido para verificar funcionamento básico"
        },
        {
            "name": "Teste Médio (150 empresas)", 
            "search_term": "dentista",
            "location": "Rio de Janeiro, RJ",
            "total": 150,
            "description": "Teste para verificar navegação espiral expandida"
        },
        {
            "name": "Teste Grande (300 empresas)",
            "search_term": "advogado",
            "location": "Belo Horizonte, MG",
            "total": 300,
            "description": "Teste para verificar navegação estendida e reset"
        }
    ]
    
    print("Configurações de teste disponíveis:")
    print()
    for i, config in enumerate(test_configs, 1):
        print(f"{i}. {config['name']}")
        print(f"   Busca: {config['search_term']} em {config['location']}")
        print(f"   Meta: {config['total']} empresas")
        print(f"   Descrição: {config['description']}")
        print()
    
    # Permite escolha manual ou teste personalizado
    print("4. Teste Personalizado")
    print()
    
    try:
        choice = int(input("Escolha uma opção (1-4): "))
        
        if choice in [1, 2, 3]:
            config = test_configs[choice - 1]
            search_term = config["search_term"]
            location = config["location"]
            total = config["total"]
            
            print(f"\n🎯 Executando: {config['name']}")
            print(f"📍 Busca: {search_term} em {location}")
            print(f"🎯 Meta: {total} empresas")
            
        elif choice == 4:
            print("\n📝 CONFIGURAÇÃO PERSONALIZADA:")
            search_term = input("Digite o termo de busca (ex: restaurante): ").strip()
            location = input("Digite a localização (ex: São Paulo, SP): ").strip()
            total = int(input("Digite o número de empresas desejado: "))
            
            print(f"\n🎯 Executando teste personalizado")
            print(f"📍 Busca: {search_term} em {location}")
            print(f"🎯 Meta: {total} empresas")
            
        else:
            print("❌ Opção inválida!")
            return
            
    except ValueError:
        print("❌ Entrada inválida!")
        return
    
    # Configurações do teste
    search_for = f"{search_term} {location}"
    save_dir = "."
    file_format = "excel"
    
    print("\n" + "=" * 80)
    print("🚀 INICIANDO EXTRAÇÃO COM NAVEGAÇÃO CONTÍNUA")
    print("=" * 80)
    print()
    print("💡 MELHORIAS IMPLEMENTADAS:")
    print("   ✅ Navegação espiral expandida (raio 15 vs 8)")
    print("   ✅ Navegação estendida com zoom")
    print("   ✅ Navegação aleatória inteligente")
    print("   ✅ Sistema de reset automático (5 tentativas)")
    print("   ✅ Limites muito mais generosos")
    print("   ✅ Logging detalhado de progresso")
    print()
    print("⏱️  TEMPO ESTIMADO:")
    if total <= 50:
        print("   📊 Pequeno (50): ~15-30 minutos")
    elif total <= 150:
        print("   📊 Médio (150): ~45-90 minutos")
    else:
        print("   📊 Grande (300+): ~2-4 horas")
    print()
    
    input("Pressione ENTER para continuar...")
    print()
    
    # Executa o scraper com as melhorias
    start_time = datetime.now()
    
    try:
        result = main_query(
            search_for=search_for,
            total=total,
            location=location,
            save_dir=save_dir,
            file_format=file_format,
            callback=test_callback
        )
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        print("\n" + "=" * 80)
        print("🎉 EXTRAÇÃO CONCLUÍDA!")
        print("=" * 80)
        print(f"⏱️  Tempo total: {duration}")
        print(f"📁 Resultado: {result}")
        print()
        print("📊 RESUMO DAS MELHORIAS:")
        print("   🟢 Navegação contínua funcionando")
        print("   🟢 Sistema de reset implementado")
        print("   🟢 Cobertura de área expandida")
        print("   🟢 Logging detalhado ativo")
        
    except KeyboardInterrupt:
        print("\n⚠️  Extração interrompida pelo usuário")
        print("💾 Dados parciais podem ter sido salvos")
        
    except Exception as e:
        print(f"\n❌ Erro durante a extração: {str(e)}")
        print("🔧 Verifique os logs acima para mais detalhes")

def main():
    """
    Função principal do script de teste.
    """
    try:
        test_navegacao_continua()
    except KeyboardInterrupt:
        print("\n👋 Teste cancelado pelo usuário")
    except Exception as e:
        print(f"\n❌ Erro no teste: {str(e)}")

if __name__ == "__main__":
    main()
